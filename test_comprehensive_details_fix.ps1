Write-Host "TESTING COMPREHENSIVE DETAILS FIX" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Yellow

# Create comprehensive test with all 36 functions
$comprehensiveTestHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Comprehensive Details Test</title></head>
<body>
<div id="result">Testing comprehensive details...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    
    // Create test data that should trigger all 36 comprehensive functions
    const testData = [{
        name: "API Authentication Bypass",
        type: "API Authentication Bypass", 
        severity: "Critical",
        description: "تم اكتشاف ثغرة تجاوز المصادقة في API",
        url: "http://testphp.vulnweb.com/api/login",
        parameter: "token",
        payload: "admin' OR '1'='1' --",
        evidence: "تم تجاوز المصادقة بنجاح",
        method: "POST",
        response: "Authentication bypassed successfully",
        extracted_real_data: {
            payload: "admin' OR '1'='1' --",
            response: "Authentication bypassed successfully",
            evidence: "تم تجاوز المصادقة بنجاح",
            url: "http://testphp.vulnweb.com/api/login",
            parameter: "token"
        }
    }];
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_DETAILS_TEST_TIMEOUT';
        }
    }, 15000);
    
    core.generateVulnerabilitiesHTML(testData)
        .then(result => {
            completed = true;
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            if (result && result.length > 1000) {
                // Check for comprehensive details
                const hasComprehensiveDetails = result.includes('تفاصيل شاملة تفصيلية');
                const hasExploitationSteps = result.includes('خطوات الاستغلال الشاملة');
                const hasDynamicImpact = result.includes('تحليل التأثير الديناميكي');
                const hasRecommendations = result.includes('التوصيات الديناميكية');
                const hasVisualAnalysis = result.includes('التصورات البصرية');
                const hasTextualAnalysis = result.includes('التحليل النصي الشامل');
                const hasSecurityAnalysis = result.includes('تحليل التأثير الأمني');
                const hasRiskAnalysis = result.includes('تحليل المخاطر الشامل');
                const hasThreatModeling = result.includes('نمذجة التهديدات');
                const hasPayloadAnalysis = result.includes('تحليل الـ Payload');
                const hasResponseAnalysis = result.includes('تحليل الاستجابة');
                const hasDocumentation = result.includes('التوثيق الشامل');
                const hasTechnicalReport = result.includes('التقرير التقني');
                const hasExecutiveSummary = result.includes('الملخص التنفيذي');
                const hasComplianceReport = result.includes('تقرير الامتثال');
                const hasForensicAnalysis = result.includes('التحليل الجنائي');
                const has36Functions = result.includes('36 دالة');
                
                const comprehensiveScore = [
                    hasComprehensiveDetails, hasExploitationSteps, hasDynamicImpact,
                    hasRecommendations, hasVisualAnalysis, hasTextualAnalysis,
                    hasSecurityAnalysis, hasRiskAnalysis, hasThreatModeling,
                    hasPayloadAnalysis, hasResponseAnalysis, hasDocumentation,
                    hasTechnicalReport, hasExecutiveSummary, hasComplianceReport,
                    hasForensicAnalysis, has36Functions
                ].filter(Boolean).length;
                
                if (comprehensiveScore >= 10) {
                    document.getElementById('result').innerHTML = 
                        'COMPREHENSIVE_DETAILS_SUCCESS_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        comprehensiveScore + '_of_17_features_found';
                } else {
                    document.getElementById('result').innerHTML = 
                        'COMPREHENSIVE_DETAILS_PARTIAL_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        comprehensiveScore + '_of_17_features_found';
                }
            } else {
                document.getElementById('result').innerHTML = 'COMPREHENSIVE_DETAILS_FAILED_SHORT_RESULT_' + result.length;
            }
        })
        .catch(err => {
            completed = true;
            clearTimeout(timeout);
            if (err.message.includes('Maximum call stack size exceeded')) {
                document.getElementById('result').innerHTML = 'COMPREHENSIVE_DETAILS_FAILED_STACK_OVERFLOW';
            } else {
                document.getElementById('result').innerHTML = 'COMPREHENSIVE_DETAILS_ERROR_' + err.message.replace(/\s+/g, '_');
            }
        });
    
} catch(e) {
    document.getElementById('result').innerHTML = 'COMPREHENSIVE_DETAILS_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

Write-Host "Creating comprehensive details test..." -ForegroundColor Cyan
$comprehensiveTestHTML | Out-File -FilePath "comprehensive_details_test.html" -Encoding UTF8

Write-Host "Starting comprehensive details test..." -ForegroundColor Yellow
Start-Process "comprehensive_details_test.html"

Write-Host "Waiting 20 seconds for comprehensive test..." -ForegroundColor Cyan
Start-Sleep -Seconds 20

Write-Host ""
Write-Host "COMPREHENSIVE DETAILS TEST COMPLETED!" -ForegroundColor Green
Write-Host "Check comprehensive_details_test.html for results:" -ForegroundColor Cyan
Write-Host "  - COMPREHENSIVE_DETAILS_SUCCESS_*: All comprehensive details working" -ForegroundColor Green
Write-Host "  - COMPREHENSIVE_DETAILS_PARTIAL_*: Some details missing" -ForegroundColor Yellow
Write-Host "  - COMPREHENSIVE_DETAILS_FAILED_*: Major issues" -ForegroundColor Red
