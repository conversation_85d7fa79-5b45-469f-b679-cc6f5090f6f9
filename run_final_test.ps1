Write-Host "RUNNING FINAL COMPREHENSIVE REPORT TEST" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Yellow

Write-Host "Starting test..." -ForegroundColor Cyan
Start-Process "final_test.html"

Write-Host "Waiting 20 seconds..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

Write-Host ""
Write-Host "CHECKING RESULTS..." -ForegroundColor Cyan

if (Test-Path "final_test_report.html") {
    $reportSize = (Get-Item "final_test_report.html").Length
    Write-Host "SUCCESS: Report generated!" -ForegroundColor Green
    Write-Host "Report size: $reportSize bytes" -ForegroundColor White
    
    $content = Get-Content "final_test_report.html" -Raw
    
    Write-Host ""
    Write-Host "CONTENT ANALYSIS:" -ForegroundColor Yellow
    
    if ($content -match "comprehensive_details") {
        Write-Host "✅ Has comprehensive_details" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing comprehensive_details" -ForegroundColor Red
    }
    
    if ($content -match "exploitation_steps") {
        Write-Host "✅ Has exploitation_steps" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing exploitation_steps" -ForegroundColor Red
    }
    
    if ($content -match "dynamic_impact") {
        Write-Host "✅ Has dynamic_impact" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing dynamic_impact" -ForegroundColor Red
    }
    
    if ($content -match "visual_impact_data") {
        Write-Host "✅ Has visual_impact_data" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing visual_impact_data" -ForegroundColor Red
    }
    
    if ($content -match "textual_impact_analysis") {
        Write-Host "✅ Has textual_impact_analysis" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing textual_impact_analysis" -ForegroundColor Red
    }
    
    if ($content -match "security_impact_analysis") {
        Write-Host "✅ Has security_impact_analysis" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing security_impact_analysis" -ForegroundColor Red
    }
    
    if ($content -match "risk_analysis") {
        Write-Host "✅ Has risk_analysis" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing risk_analysis" -ForegroundColor Red
    }
    
    if ($content -match "threat_modeling") {
        Write-Host "✅ Has threat_modeling" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing threat_modeling" -ForegroundColor Red
    }
    
    if ($content -match "business_impact_analysis") {
        Write-Host "✅ Has business_impact_analysis" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing business_impact_analysis" -ForegroundColor Red
    }
    
    if ($content -match "SystemConfigV4") {
        Write-Host "✅ Has SystemConfigV4" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing SystemConfigV4" -ForegroundColor Red
    }
    
    if ($content -match "SystemVerificationV4") {
        Write-Host "✅ Has SystemVerificationV4" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing SystemVerificationV4" -ForegroundColor Red
    }
    
    if ($content -match "ReportExporter") {
        Write-Host "✅ Has ReportExporter" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing ReportExporter" -ForegroundColor Red
    }
    
    if ($content -match "TestSystem") {
        Write-Host "✅ Has TestSystem" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing TestSystem" -ForegroundColor Red
    }
    
    if ($content -match "PythonScreenshotBridge") {
        Write-Host "✅ Has PythonScreenshotBridge" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing PythonScreenshotBridge" -ForegroundColor Red
    }
    
    if ($content -notmatch "error|خطأ") {
        Write-Host "✅ No errors found" -ForegroundColor Green
    } else {
        Write-Host "❌ Contains errors" -ForegroundColor Red
    }
    
    Write-Host ""
    if ($reportSize -gt 15000) {
        Write-Host "EXCELLENT: Very comprehensive report!" -ForegroundColor Green
    } elseif ($reportSize -gt 10000) {
        Write-Host "GOOD: Comprehensive report!" -ForegroundColor Yellow
    } elseif ($reportSize -gt 5000) {
        Write-Host "MODERATE: Basic report" -ForegroundColor Yellow
    } else {
        Write-Host "POOR: Report too small" -ForegroundColor Red
    }
    
} else {
    Write-Host "FAILED: No report generated!" -ForegroundColor Red
}

Write-Host ""
Write-Host "FINAL TEST COMPLETED!" -ForegroundColor Green
