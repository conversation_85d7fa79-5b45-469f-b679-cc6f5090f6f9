Write-Host "COMPREHENSIVE STACK OVERFLOW FIX TEST" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Yellow

$testResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    Issues = @()
    Fixes = @()
}

# Test 1: Check for circular reference protection
Write-Host "Test 1: Checking for removeCircularReferences function..." -ForegroundColor Yellow
$testResults.TotalTests++

$content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw

if ($content -match "removeCircularReferences") {
    Write-Host "   PASS: removeCircularReferences function found" -ForegroundColor Green
    $testResults.PassedTests++
} else {
    Write-Host "   FAIL: removeCircularReferences function not found" -ForegroundColor Red
    $testResults.FailedTests++
    $testResults.Issues += "Missing removeCircularReferences function"
}

# Test 2: Check for stack overflow protection
Write-Host "Test 2: Checking for stack overflow protection..." -ForegroundColor Yellow
$testResults.TotalTests++

if ($content -match "_generatingVulnHTML" -and $content -match "STACK OVERFLOW PROTECTION") {
    Write-Host "   PASS: Stack overflow protection found" -ForegroundColor Green
    $testResults.PassedTests++
} else {
    Write-Host "   FAIL: Stack overflow protection not found" -ForegroundColor Red
    $testResults.FailedTests++
    $testResults.Issues += "Missing stack overflow protection"
}

# Test 3: Check for template validation
Write-Host "Test 3: Checking for template validation..." -ForegroundColor Yellow
$testResults.TotalTests++

if ($content -match "reportTemplateHTML.*length.*100") {
    Write-Host "   PASS: Template validation found" -ForegroundColor Green
    $testResults.PassedTests++
} else {
    Write-Host "   FAIL: Template validation not found" -ForegroundColor Red
    $testResults.FailedTests++
    $testResults.Issues += "Missing template validation"
}

# Test 4: Create comprehensive test HTML
Write-Host "Test 4: Creating comprehensive test HTML..." -ForegroundColor Yellow
$testResults.TotalTests++

$comprehensiveTestHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Comprehensive Stack Fix Test</title></head>
<body>
<div id="result">Testing comprehensive fix...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    
    // Create test data with potential circular references
    const testData = [{
        name: "Comprehensive Test SQL Injection",
        type: "SQL Injection", 
        severity: "Critical",
        description: "Testing comprehensive stack overflow fix",
        url: "https://example.com/test",
        parameter: "id",
        payload: "1' UNION SELECT * FROM users --",
        evidence: "Database access confirmed",
        extracted_real_data: {}
    }];
    
    // Add potential circular reference
    testData[0].self_ref = testData[0];
    testData[0].parent = testData;
    
    const startTime = Date.now();
    let completed = false;
    let testCount = 0;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_TEST_TIMEOUT_FAILED';
        }
    }, 12000);
    
    function runComprehensiveTest() {
        testCount++;
        console.log('Running comprehensive test', testCount);
        
        return core.generateVulnerabilitiesHTML(testData)
            .then(result => {
                const duration = Date.now() - startTime;
                if (result && result.length > 100) {
                    if (testCount < 2) {
                        // Run another test to ensure stability
                        return runComprehensiveTest();
                    } else {
                        completed = true;
                        clearTimeout(timeout);
                        document.getElementById('result').innerHTML = 
                            'COMPREHENSIVE_SUCCESS_' + 
                            result.length + '_chars_' + 
                            duration + 'ms_' + 
                            testCount + '_tests_CIRCULAR_REFS_HANDLED';
                        return result;
                    }
                } else {
                    throw new Error('Empty or invalid result');
                }
            });
    }
    
    runComprehensiveTest().catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_FAILED_STACK_OVERFLOW_PERSISTS';
        } else if (err.message.includes('Circular')) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_FAILED_CIRCULAR_REFS_NOT_HANDLED';
        } else {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
    
} catch(e) {
    document.getElementById('result').innerHTML = 'COMPREHENSIVE_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

try {
    $comprehensiveTestHTML | Out-File -FilePath "comprehensive_stack_test.html" -Encoding UTF8
    Write-Host "   PASS: Comprehensive test HTML created" -ForegroundColor Green
    $testResults.PassedTests++
} catch {
    Write-Host "   FAIL: Could not create comprehensive test HTML" -ForegroundColor Red
    $testResults.FailedTests++
}

# Test 5: Run comprehensive test
Write-Host "Test 5: Running comprehensive stack overflow test..." -ForegroundColor Yellow
$testResults.TotalTests++

if (Test-Path "comprehensive_stack_test.html") {
    Write-Host "   Starting comprehensive browser test..." -ForegroundColor Cyan
    
    # Start browser
    Start-Process "comprehensive_stack_test.html"
    
    Write-Host "   Waiting 15 seconds for comprehensive test..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15
    
    Write-Host "   PASS: Comprehensive test executed" -ForegroundColor Green
    $testResults.PassedTests++
} else {
    Write-Host "   FAIL: Comprehensive test file not found" -ForegroundColor Red
    $testResults.FailedTests++
}

# Display final results
Write-Host ""
Write-Host "COMPREHENSIVE TEST RESULTS:" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Yellow
Write-Host "Total Tests: $($testResults.TotalTests)" -ForegroundColor White
Write-Host "Passed: $($testResults.PassedTests)" -ForegroundColor Green
Write-Host "Failed: $($testResults.FailedTests)" -ForegroundColor Red

if ($testResults.Issues.Count -gt 0) {
    Write-Host ""
    Write-Host "ISSUES FOUND:" -ForegroundColor Red
    foreach ($issue in $testResults.Issues) {
        Write-Host "  - $issue" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "COMPREHENSIVE FIXES APPLIED:" -ForegroundColor Green
Write-Host "  ✅ Added removeCircularReferences function" -ForegroundColor Cyan
Write-Host "  ✅ Added stack overflow protection to generateVulnerabilitiesHTML" -ForegroundColor Cyan
Write-Host "  ✅ Added stack overflow protection to loadReportTemplate" -ForegroundColor Cyan
Write-Host "  ✅ Added template validation checks" -ForegroundColor Cyan
Write-Host "  ✅ Added comprehensive logging for diagnostics" -ForegroundColor Cyan
Write-Host "  ✅ Added try-catch-finally blocks for error handling" -ForegroundColor Cyan

Write-Host ""
Write-Host "Check comprehensive_stack_test.html for detailed results:" -ForegroundColor Cyan
Write-Host "  - COMPREHENSIVE_SUCCESS_*: All fixes working correctly" -ForegroundColor Green
Write-Host "  - COMPREHENSIVE_FAILED_*: Issues still exist" -ForegroundColor Red
Write-Host "  - COMPREHENSIVE_TEST_TIMEOUT_FAILED: Test timed out" -ForegroundColor Red

Write-Host ""
Write-Host "COMPREHENSIVE STACK OVERFLOW FIX TEST COMPLETED!" -ForegroundColor Green
