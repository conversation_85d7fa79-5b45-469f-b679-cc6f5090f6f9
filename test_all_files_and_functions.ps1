Write-Host "TESTING ALL FILES AND 36 FUNCTIONS - COMPREHENSIVE SYSTEM" -ForegroundColor Green
Write-Host "=========================================================" -ForegroundColor Yellow

# Create comprehensive test for all files and functions
$allFilesTestHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>All Files and Functions Test</title></head>
<body>
<div id="result">Testing all files and 36 functions...</div>
<div id="console-output" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 600px; overflow-y: auto;"></div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
// Capture console.log output
const originalLog = console.log;
const consoleOutput = document.getElementById('console-output');
console.log = function(...args) {
    originalLog.apply(console, args);
    consoleOutput.textContent += args.join(' ') + '\n';
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
};

try {
    const core = new BugBountyCore();
    
    // Create comprehensive test data
    const testData = [{
        name: "Ultimate All Files and Functions Test",
        type: "API Authentication Bypass", 
        severity: "Critical",
        description: "اختبار شامل لجميع الملفات والدوال الـ 36 في النظام v4.0",
        url: "http://testphp.vulnweb.com/api/login",
        parameter: "token",
        payload: "admin' OR '1'='1' --",
        evidence: "تم تجاوز المصادقة بنجاح - اختبار شامل لجميع الملفات",
        method: "POST",
        response: "Authentication bypassed successfully - All Files Test",
        confidence_level: 95,
        extracted_real_data: {
            payload: "admin' OR '1'='1' --",
            response: "Authentication bypassed successfully - All Files Test", 
            evidence: "تم تجاوز المصادقة بنجاح - اختبار شامل لجميع الملفات",
            url: "http://testphp.vulnweb.com/api/login",
            parameter: "token",
            method: "POST",
            status_code: 200,
            response_time: 150,
            headers: {"Content-Type": "application/json"},
            body: '{"success": true, "user": "admin", "all_files_test": true}',
            exploitation_confirmed: true,
            vulnerability_confirmed: true,
            testing_results: {
                successful: true,
                payload_executed: true,
                response_indicates_success: true,
                all_files_test: true
            }
        }
    }];
    
    console.log('🚀 بدء الاختبار الشامل لجميع الملفات والدوال الـ 36');
    console.log('📋 بيانات الاختبار الشامل:', JSON.stringify(testData[0], null, 2));
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_TEST_TIMEOUT';
        }
    }, 50000);
    
    core.generateVulnerabilitiesHTML(testData)
        .then(result => {
            completed = true;
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            console.log('📊 نتائج الاختبار الشامل لجميع الملفات والدوال:');
            console.log('- طول النتيجة:', result.length);
            console.log('- وقت التنفيذ:', duration + 'ms');
            console.log('- أول 2000 حرف:', result.substring(0, 2000));
            
            if (result && result.length > 15000) {
                // Check for all comprehensive content from 36 functions
                const has36Functions = [
                    result.includes('comprehensive_details') || result.includes('التفاصيل الشاملة'),
                    result.includes('exploitation_steps') || result.includes('خطوات الاستغلال'),
                    result.includes('dynamic_impact') || result.includes('تحليل التأثير'),
                    result.includes('visual_impact_data') || result.includes('التصورات البصرية'),
                    result.includes('textual_impact_analysis') || result.includes('التحليل النصي'),
                    result.includes('security_impact_analysis') || result.includes('تحليل التأثير الأمني'),
                    result.includes('risk_analysis') || result.includes('تحليل المخاطر'),
                    result.includes('threat_modeling') || result.includes('نمذجة التهديدات'),
                    result.includes('business_impact_analysis') || result.includes('التأثير التجاري'),
                    result.includes('comprehensive_textual_report') || result.includes('التقرير النصي الشامل'),
                    result.includes('detailed_textual_analysis') || result.includes('التحليل النصي المفصل')
                ].filter(Boolean).length;
                
                // Check for all comprehensive files
                const hasAllFiles = [
                    result.includes('impact_visualizer.js') || result.includes('التصورات البصرية'),
                    result.includes('textual_impact_analyzer.js') || result.includes('التحليل النصي'),
                    result.includes('SystemConfigV4') || result.includes('تحليل تكوين النظام'),
                    result.includes('SystemVerificationV4') || result.includes('نتائج التحقق'),
                    result.includes('ReportExporter') || result.includes('تنسيقات التصدير'),
                    result.includes('TestSystem') || result.includes('نتائج الاختبار'),
                    result.includes('PythonScreenshotBridge') || result.includes('تحليل Python')
                ].filter(Boolean).length;
                
                const hasRealContent = !result.includes('لم يتم إنتاج');
                const hasNoErrors = !result.includes('خطأ في إنشاء التقرير');
                
                console.log('✅ فحص المحتوى الشامل لجميع الملفات والدوال:');
                console.log('- الدوال الـ 36:', has36Functions, 'من 11');
                console.log('- الملفات الشاملة:', hasAllFiles, 'من 7');
                console.log('- محتوى حقيقي:', hasRealContent);
                console.log('- بدون أخطاء:', hasNoErrors);
                
                const totalScore = has36Functions + hasAllFiles + (hasRealContent ? 1 : 0) + (hasNoErrors ? 1 : 0);
                
                if (totalScore >= 16) {
                    document.getElementById('result').innerHTML = 
                        'ALL_FILES_FUNCTIONS_SUCCESS_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        has36Functions + '_functions_' +
                        hasAllFiles + '_files_' +
                        'PERFECT_COMPREHENSIVE_SYSTEM';
                } else if (totalScore >= 12) {
                    document.getElementById('result').innerHTML = 
                        'ALL_FILES_FUNCTIONS_GOOD_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        has36Functions + '_functions_' +
                        hasAllFiles + '_files_' +
                        'MOST_WORKING';
                } else {
                    document.getElementById('result').innerHTML = 
                        'ALL_FILES_FUNCTIONS_PARTIAL_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        has36Functions + '_functions_' +
                        hasAllFiles + '_files_' +
                        'SOME_WORKING';
                }
            } else {
                console.log('❌ النتيجة قصيرة جداً');
                document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_FAILED_SHORT_RESULT_' + result.length;
            }
        })
        .catch(err => {
            completed = true;
            clearTimeout(timeout);
            console.log('❌ خطأ في الاختبار الشامل:', err.message);
            console.log('❌ تفاصيل الخطأ:', err.stack);
            document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_ERROR_' + err.message.replace(/\s+/g, '_');
        });
    
} catch(e) {
    console.log('❌ خطأ في السكريبت الشامل:', e.message);
    console.log('❌ تفاصيل الخطأ:', e.stack);
    document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

Write-Host "Creating all files and functions test..." -ForegroundColor Cyan
$allFilesTestHTML | Out-File -FilePath "all_files_and_functions_test.html" -Encoding UTF8

Write-Host "Starting all files and functions test..." -ForegroundColor Yellow
Start-Process "all_files_and_functions_test.html"

Write-Host "Waiting 55 seconds for comprehensive test..." -ForegroundColor Cyan
Start-Sleep -Seconds 55

Write-Host ""
Write-Host "ALL FILES AND FUNCTIONS TEST COMPLETED!" -ForegroundColor Green
Write-Host "Check all_files_and_functions_test.html for results:" -ForegroundColor Cyan
Write-Host "  - ALL_FILES_FUNCTIONS_SUCCESS_*: Perfect comprehensive system!" -ForegroundColor Green
Write-Host "  - ALL_FILES_FUNCTIONS_GOOD_*: Most files and functions working" -ForegroundColor Yellow
Write-Host "  - ALL_FILES_FUNCTIONS_PARTIAL_*: Some files and functions working" -ForegroundColor Red
Write-Host "  - ALL_FILES_FUNCTIONS_FAILED_*: Major issues with files/functions" -ForegroundColor Red
