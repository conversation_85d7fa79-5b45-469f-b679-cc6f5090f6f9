Write-Host "🔧 اختبار وإصلاح مشكلة Maximum call stack size exceeded" -ForegroundColor Yellow
Write-Host "=======================================================" -ForegroundColor Cyan

$global:testResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    Issues = @()
    Fixes = @()
}

function Test-Function {
    param(
        [string]$TestName,
        [string]$Description,
        [scriptblock]$TestScript
    )
    
    $global:testResults.TotalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $result = & $TestScript
        if ($result) {
            Write-Host "   ✅ نجح" -ForegroundColor Green
            $global:testResults.PassedTests++
        } else {
            Write-Host "   ❌ فشل" -ForegroundColor Red
            $global:testResults.FailedTests++
            $global:testResults.Issues += $TestName
        }
    } catch {
        Write-Host "   ❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
        $global:testResults.FailedTests++
        $global:testResults.Issues += "$TestName - $($_.Exception.Message)"
    }
    Write-Host ""
}

# اختبار 1: فحص وجود الملف
Test-Function -TestName "فحص وجود الملف" -Description "التحقق من وجود BugBountyCore.js" -TestScript {
    Test-Path "assets\modules\bugbounty\BugBountyCore.js"
}

# اختبار 2: فحص الاستدعاءات المتكررة الخطيرة
Test-Function -TestName "فحص الاستدعاءات المتكررة" -Description "البحث عن استدعاءات متكررة قد تسبب stack overflow" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw
    
    # البحث عن استدعاءات متكررة خطيرة
    $dangerousPatterns = @(
        "generatePageHTMLReport.*generatePageHTMLReport",
        "saveAndExportPageResults.*saveAndExportPageResults",
        "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData"
    )
    
    $foundDangerous = $false
    foreach ($pattern in $dangerousPatterns) {
        if ($content -match $pattern) {
            Write-Host "   ⚠️ تم العثور على استدعاء متكرر خطير: $pattern" -ForegroundColor Red
            $foundDangerous = $true
            $global:testResults.Issues += "استدعاء متكرر: $pattern"
        }
    }
    
    return -not $foundDangerous
}

# اختبار 3: فحص استدعاء generatePageHTMLReport من داخل saveAndExportPageResults
Test-Function -TestName "فحص الاستدعاء المتكرر الرئيسي" -Description "فحص استدعاء generatePageHTMLReport من saveAndExportPageResults" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw
    
    # البحث عن saveAndExportPageResults
    if ($content -match "async saveAndExportPageResults\(.*?\{(.*?)\}") {
        $functionContent = $matches[1]
        if ($functionContent -match "generatePageHTMLReport") {
            Write-Host "   ❌ تم العثور على استدعاء generatePageHTMLReport داخل saveAndExportPageResults" -ForegroundColor Red
            $global:testResults.Issues += "استدعاء متكرر في saveAndExportPageResults"
            return $false
        }
    }
    
    return $true
}

# اختبار 4: إنشاء اختبار HTML مباشر
Test-Function -TestName "إنشاء اختبار HTML" -Description "إنشاء ملف اختبار HTML للتحقق من المشكلة" -TestScript {
    $testHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Stack Overflow Test</title></head>
<body>
<div id="result">Testing...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {vulnerabilities: [{name: "Test", type: "XSS", extracted_real_data: {}}]};
    
    const startTime = Date.now();
    const timeout = setTimeout(() => {
        document.getElementById('result').innerHTML = 'TIMEOUT: Maximum call stack size exceeded likely occurred';
    }, 10000);
    
    core.generatePageHTMLReport(testData, "test", 1).then(result => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        if (result && result.length > 100) {
            document.getElementById('result').innerHTML = 'SUCCESS: ' + result.length + ' chars in ' + duration + 'ms';
        } else {
            document.getElementById('result').innerHTML = 'FAILED: Empty or short result';
        }
    }).catch(err => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'STACK_OVERFLOW: ' + duration + 'ms';
        } else {
            document.getElementById('result').innerHTML = 'ERROR: ' + err.message;
        }
    });
} catch(e) {
    document.getElementById('result').innerHTML = 'SCRIPT_ERROR: ' + e.message;
}
</script>
</body></html>
"@
    
    $testHTML | Out-File -FilePath "stack_test_auto.html" -Encoding UTF8
    return Test-Path "stack_test_auto.html"
}

# اختبار 5: تشغيل الاختبار التلقائي
Test-Function -TestName "تشغيل الاختبار التلقائي" -Description "تشغيل الاختبار في المتصفح والتحقق من النتيجة" -TestScript {
    if (Test-Path "stack_test_auto.html") {
        Write-Host "   🔄 تشغيل الاختبار في المتصفح..." -ForegroundColor Cyan
        
        # تشغيل المتصفح
        Start-Process "stack_test_auto.html"
        
        # انتظار 15 ثانية للاختبار
        Write-Host "   ⏱️ انتظار 15 ثانية للاختبار..." -ForegroundColor Yellow
        Start-Sleep -Seconds 15
        
        return $true
    }
    return $false
}

# تشغيل جميع الاختبارات
Write-Host "🚀 بدء تشغيل الاختبارات التلقائية..." -ForegroundColor Green
Write-Host ""

# تشغيل الاختبارات
$tests = @(
    @{ Name = "فحص وجود الملف"; Description = "التحقق من وجود BugBountyCore.js" },
    @{ Name = "فحص الاستدعاءات المتكررة"; Description = "البحث عن استدعاءات متكررة" },
    @{ Name = "فحص الاستدعاء المتكرر الرئيسي"; Description = "فحص الاستدعاء المتكرر" },
    @{ Name = "إنشاء اختبار HTML"; Description = "إنشاء ملف اختبار" },
    @{ Name = "تشغيل الاختبار التلقائي"; Description = "تشغيل الاختبار" }
)

# عرض النتائج النهائية
Write-Host "📊 نتائج الاختبارات:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Yellow
Write-Host "إجمالي الاختبارات: $($global:testResults.TotalTests)" -ForegroundColor White
Write-Host "نجح: $($global:testResults.PassedTests)" -ForegroundColor Green
Write-Host "فشل: $($global:testResults.FailedTests)" -ForegroundColor Red

if ($global:testResults.Issues.Count -gt 0) {
    Write-Host ""
    Write-Host "🚨 المشاكل المكتشفة:" -ForegroundColor Red
    foreach ($issue in $global:testResults.Issues) {
        Write-Host "   • $issue" -ForegroundColor Yellow
    }
}

if ($global:testResults.FailedTests -gt 0) {
    Write-Host ""
    Write-Host "🔧 بدء الإصلاح التلقائي..." -ForegroundColor Yellow
    
    # إصلاح المشكلة إذا تم اكتشافها
    if ($global:testResults.Issues -contains "استدعاء متكرر في saveAndExportPageResults") {
        Write-Host "🔧 إصلاح الاستدعاء المتكرر في saveAndExportPageResults..." -ForegroundColor Yellow
        
        # قراءة الملف
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw
        
        # البحث عن السطر المشكل وإصلاحه
        $fixedContent = $content -replace "const pageReport = await this\.generatePageHTMLReport\(pageResult, pageUrl, pageNumber\);", "// تم تعطيل الاستدعاء المتكرر - const pageReport = await this.generatePageHTMLReport(pageResult, pageUrl, pageNumber);"
        
        # حفظ الملف المُصلح
        $fixedContent | Out-File -FilePath "assets\modules\bugbounty\BugBountyCore.js" -Encoding UTF8
        
        Write-Host "✅ تم إصلاح الاستدعاء المتكرر" -ForegroundColor Green
        $global:testResults.Fixes += "إصلاح الاستدعاء المتكرر في saveAndExportPageResults"
    }
}

Write-Host ""
Write-Host "🏁 انتهى الاختبار التلقائي" -ForegroundColor Green
Write-Host "تحقق من ملف stack_test_auto.html للنتائج التفصيلية" -ForegroundColor Cyan
