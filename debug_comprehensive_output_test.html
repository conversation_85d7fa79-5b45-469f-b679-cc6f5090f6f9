﻿<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Debug Comprehensive Output</title></head>
<body>
<div id="result">Debugging comprehensive output...</div>
<div id="console-output" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
// Capture console.log output
const originalLog = console.log;
const consoleOutput = document.getElementById('console-output');
console.log = function(...args) {
    originalLog.apply(console, args);
    consoleOutput.textContent += args.join(' ') + '\n';
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
};

try {
    const core = new BugBountyCore();
    
    // Create test data with extracted_real_data
    const testData = [{
        name: "API Authentication Bypass Debug Test",
        type: "API Authentication Bypass", 
        severity: "Critical",
        description: "طھظ… ط§ظƒطھط´ط§ظپ ط«ط؛ط±ط© طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ظپظٹ API - ط§ط®طھط¨ط§ط± طھط´ط®ظٹطµظٹ",
        url: "http://testphp.vulnweb.com/api/login",
        parameter: "token",
        payload: "admin' OR '1'='1' --",
        evidence: "طھظ… طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ط¨ظ†ط¬ط§ط­",
        method: "POST",
        response: "Authentication bypassed successfully",
        confidence_level: 95,
        extracted_real_data: {
            payload: "admin' OR '1'='1' --",
            response: "Authentication bypassed successfully", 
            evidence: "طھظ… طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ط¨ظ†ط¬ط§ط­",
            url: "http://testphp.vulnweb.com/api/login",
            parameter: "token",
            method: "POST",
            status_code: 200,
            response_time: 150,
            headers: {"Content-Type": "application/json"},
            body: '{"success": true, "user": "admin"}',
            exploitation_confirmed: true,
            vulnerability_confirmed: true,
            testing_results: {
                successful: true,
                payload_executed: true,
                response_indicates_success: true
            }
        }
    }];
    
    console.log('ًںڑ€ ط¨ط¯ط، ط§ط®طھط¨ط§ط± طھط´ط®ظٹطµظٹ ظ„ظ„ط¯ظˆط§ظ„ ط§ظ„ظ€ 36');
    console.log('ًں“‹ ط¨ظٹط§ظ†ط§طھ ط§ظ„ط§ط®طھط¨ط§ط±:', JSON.stringify(testData[0], null, 2));
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'DEBUG_TEST_TIMEOUT';
        }
    }, 25000);
    
    core.generateVulnerabilitiesHTML(testData)
        .then(result => {
            completed = true;
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            console.log('ًں“ٹ ظ†طھط§ط¦ط¬ ط§ظ„ط§ط®طھط¨ط§ط± ط§ظ„طھط´ط®ظٹطµظٹ:');
            console.log('- ط·ظˆظ„ ط§ظ„ظ†طھظٹط¬ط©:', result.length);
            console.log('- ظˆظ‚طھ ط§ظ„طھظ†ظپظٹط°:', duration + 'ms');
            console.log('- ط£ظˆظ„ 500 ط­ط±ظپ:', result.substring(0, 500));
            console.log('- ط¢ط®ط± 500 ط­ط±ظپ:', result.substring(Math.max(0, result.length - 500)));
            
            if (result && result.length > 2000) {
                // Check for specific content
                const hasRealDetails = result.includes('ط§ظ„طھظپط§طµظٹظ„ ط§ظ„طھظ‚ظ†ظٹط©');
                const hasImpactAnalysis = result.includes('طھط­ظ„ظٹظ„ ط§ظ„طھط£ط«ظٹط±');
                const hasExploitationResults = result.includes('ظ†طھط§ط¦ط¬ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„');
                const hasComprehensiveContent = result.includes('comprehensive_details');
                const hasGeneratedContent = !result.includes('ظ„ظ… ظٹطھظ… ط¥ظ†طھط§ط¬');
                
                console.log('âœ… ظپط­طµ ط§ظ„ظ…ط­طھظˆظ‰:');
                console.log('- ط§ظ„طھظپط§طµظٹظ„ ط§ظ„طھظ‚ظ†ظٹط©:', hasRealDetails);
                console.log('- طھط­ظ„ظٹظ„ ط§ظ„طھط£ط«ظٹط±:', hasImpactAnalysis);
                console.log('- ظ†طھط§ط¦ط¬ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„:', hasExploitationResults);
                console.log('- ظ…ط­طھظˆظ‰ ط´ط§ظ…ظ„:', hasComprehensiveContent);
                console.log('- ظ…ط­طھظˆظ‰ ظ…ظڈظ†طھط¬:', hasGeneratedContent);
                
                const score = [hasRealDetails, hasImpactAnalysis, hasExploitationResults, hasComprehensiveContent, hasGeneratedContent].filter(Boolean).length;
                
                document.getElementById('result').innerHTML = 
                    'DEBUG_SUCCESS_' + 
                    result.length + '_chars_' + 
                    duration + 'ms_' + 
                    score + '_of_5_checks_passed';
            } else {
                console.log('â‌Œ ط§ظ„ظ†طھظٹط¬ط© ظ‚طµظٹط±ط© ط¬ط¯ط§ظ‹');
                document.getElementById('result').innerHTML = 'DEBUG_FAILED_SHORT_RESULT_' + result.length;
            }
        })
        .catch(err => {
            completed = true;
            clearTimeout(timeout);
            console.log('â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„ط§ط®طھط¨ط§ط±:', err.message);
            console.log('â‌Œ طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£:', err.stack);
            document.getElementById('result').innerHTML = 'DEBUG_ERROR_' + err.message.replace(/\s+/g, '_');
        });
    
} catch(e) {
    console.log('â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„ط³ظƒط±ظٹط¨طھ:', e.message);
    console.log('â‌Œ طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£:', e.stack);
    document.getElementById('result').innerHTML = 'DEBUG_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
