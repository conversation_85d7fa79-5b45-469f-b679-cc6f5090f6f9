﻿<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Stack Test</title></head>
<body>
<div id="result">Testing...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {vulnerabilities: [{name: "Test", type: "XSS", extracted_real_data: {}}]};
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'TIMEOUT_STACK_OVERFLOW';
        }
    }, 8000);
    
    core.generatePageHTMLReport(testData, "test", 1).then(result => {
        completed = true;
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        if (result && result.length > 100) {
            document.getElementById('result').innerHTML = 'SUCCESS_' + result.length + '_' + duration;
        } else {
            document.getElementById('result').innerHTML = 'FAILED_EMPTY_RESULT';
        }
    }).catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'STACK_OVERFLOW_CONFIRMED';
        } else {
            document.getElementById('result').innerHTML = 'ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
} catch(e) {
    document.getElementById('result').innerHTML = 'SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
