<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار إصلاح Maximum call stack size exceeded</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .result { font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 اختبار إصلاح Maximum call stack size exceeded</h1>
    <div id="status" class="status info">🔄 جاري التحضير...</div>
    <button onclick="testStackFix()">🚀 اختبار الإصلاح</button>
    <div id="result" class="result"></div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        function logMessage(message) {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        async function testStackFix() {
            updateStatus('🔄 بدء اختبار الإصلاح...', 'info');
            logMessage('🚀 بدء اختبار إصلاح Maximum call stack size exceeded');
            
            try {
                // إنشاء مثيل BugBountyCore
                const bugBountyCore = new BugBountyCore();
                logMessage('✅ تم إنشاء مثيل BugBountyCore بنجاح');

                // بيانات اختبار
                const testData = {
                    vulnerabilities: [{
                        name: 'اختبار SQL Injection للإصلاح',
                        type: 'SQL Injection',
                        severity: 'Critical',
                        description: 'ثغرة اختبار لفحص الإصلاح',
                        payload: "admin' OR '1'='1' --",
                        url: 'https://example.com/login',
                        parameter: 'username',
                        method: 'POST',
                        response: 'تم تسجيل الدخول بنجاح',
                        evidence: 'تم الوصول للوحة الإدارة',
                        extracted_real_data: {
                            payload: "admin' OR '1'='1' --",
                            url: 'https://example.com/login',
                            parameter: 'username',
                            response: 'تم تسجيل الدخول بنجاح',
                            method: 'POST',
                            evidence: 'تم الوصول للوحة الإدارة'
                        }
                    }]
                };

                logMessage('📋 تم إنشاء بيانات الاختبار');

                // اختبار generatePageHTMLReport مع timeout
                updateStatus('🔄 اختبار generatePageHTMLReport...', 'info');
                logMessage('🔄 بدء اختبار generatePageHTMLReport...');

                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('TIMEOUT_15_SECONDS')), 15000);
                });

                const reportPromise = bugBountyCore.generatePageHTMLReport(testData, 'https://example.com', 1);
                
                const startTime = Date.now();
                const result = await Promise.race([reportPromise, timeoutPromise]);
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (result && result.length > 100) {
                    updateStatus('✅ نجح الاختبار!', 'success');
                    logMessage(`✅ نجح إنشاء التقرير!`);
                    logMessage(`📊 حجم التقرير: ${result.length} حرف (${(result.length / 1024).toFixed(1)} KB)`);
                    logMessage(`⏱️ وقت التنفيذ: ${duration}ms`);
                    logMessage('🎉 تم حل مشكلة Maximum call stack size exceeded بنجاح!');
                    
                    // تحميل التقرير كملف
                    const blob = new Blob([result], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `Stack_Fix_Test_Report_${Date.now()}.html`;
                    a.click();
                    URL.revokeObjectURL(url);
                    
                    logMessage('📥 تم تحميل التقرير كملف HTML');
                } else {
                    updateStatus('⚠️ نتيجة غير متوقعة', 'error');
                    logMessage('⚠️ التقرير فارغ أو قصير جداً');
                }

            } catch (error) {
                const duration = Date.now() - (window.testStartTime || Date.now());
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    updateStatus('❌ المشكلة لا تزال موجودة', 'error');
                    logMessage('❌ المشكلة لا تزال موجودة: Maximum call stack size exceeded');
                    logMessage('🔧 الإصلاح لم يعمل بشكل صحيح');
                } else if (error.message.includes('TIMEOUT')) {
                    updateStatus('⏰ انتهت مهلة الاختبار', 'error');
                    logMessage('⏰ انتهت مهلة الاختبار (15 ثانية)');
                    logMessage('⚠️ قد تكون هناك حلقة لا نهائية أو بطء في التنفيذ');
                } else {
                    updateStatus('❌ خطأ في الاختبار', 'error');
                    logMessage(`❌ خطأ في الاختبار: ${error.message}`);
                }
                
                logMessage(`⏱️ وقت الفشل: ${duration}ms`);
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            updateStatus('✅ جاهز للاختبار', 'success');
            logMessage('🚀 تم تحميل صفحة اختبار الإصلاح');
            logMessage('ℹ️ اضغط على "اختبار الإصلاح" لبدء الاختبار');
        };
    </script>
</body>
</html>
