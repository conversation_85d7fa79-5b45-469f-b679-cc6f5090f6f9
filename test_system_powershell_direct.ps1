Write-Host "DIRECT POWERSHELL TEST - ALL FILES AND 36 FUNCTIONS" -ForegroundColor Green
Write-Host "===================================================" -ForegroundColor Yellow

# Test 1: Check if all comprehensive files exist
Write-Host "Test 1: Checking comprehensive files existence..." -ForegroundColor Cyan
$comprehensiveFiles = @(
    "assets\modules\bugbounty\BugBountyCore.js",
    "assets\modules\bugbounty\impact_visualizer.js", 
    "assets\modules\bugbounty\textual_impact_analyzer.js",
    "assets\modules\bugbounty\system_config_v4.js",
    "assets\modules\bugbounty\system_verification_v4.js",
    "assets\modules\bugbounty\report_exporter.js",
    "assets\modules\bugbounty\test_system.js",
    "assets\modules\bugbounty\python_screenshot_bridge.js",
    "assets\modules\bugbounty\report_template.html"
)

$filesExist = 0
foreach ($file in $comprehensiveFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file exists" -ForegroundColor Green
        $filesExist++
    } else {
        Write-Host "  ❌ $file missing" -ForegroundColor Red
    }
}

Write-Host "Files Status: $filesExist/$($comprehensiveFiles.Count) files exist" -ForegroundColor Yellow

# Test 2: Check for 36 functions in BugBountyCore.js
Write-Host "`nTest 2: Checking for 36 comprehensive functions..." -ForegroundColor Cyan
$coreContent = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw

$functions36 = @(
    "generateComprehensiveDetailsFromRealData",
    "extractRealDataFromDiscoveredVulnerability", 
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateRealDetailedDialogueFromDiscoveredVulnerability",
    "generateComprehensiveVulnerabilityAnalysis",
    "generateDynamicSecurityImpactAnalysis",
    "generateRealTimeVulnerabilityAssessment",
    "generateComprehensiveRiskAnalysis",
    "generateDynamicThreatModelingForVulnerability",
    "generateComprehensiveTestingDetails",
    "generateVisualChangesForVulnerability",
    "generatePersistentResultsForVulnerability",
    "generateImpactVisualizationsForVulnerability",
    "captureScreenshotForVulnerability",
    "generateBeforeAfterScreenshots"
)

$functionsFound = 0
foreach ($func in $functions36) {
    if ($coreContent -match $func) {
        Write-Host "  ✅ $func found" -ForegroundColor Green
        $functionsFound++
    } else {
        Write-Host "  ❌ $func missing" -ForegroundColor Red
    }
}

Write-Host "Functions Status: $functionsFound/$($functions36.Count) functions found" -ForegroundColor Yellow

# Test 3: Check for missing functions in impact_visualizer.js
Write-Host "`nTest 3: Checking impact_visualizer.js functions..." -ForegroundColor Cyan
$impactContent = Get-Content "assets\modules\bugbounty\impact_visualizer.js" -Raw

$impactFunctions = @(
    "captureExploitationScreenshots",
    "generateBeforeAfterComparison", 
    "generateRealTimeVisualAnalysis",
    "createVulnerabilityVisualization"
)

$impactFound = 0
foreach ($func in $impactFunctions) {
    if ($impactContent -match $func) {
        Write-Host "  ✅ $func found in impact_visualizer.js" -ForegroundColor Green
        $impactFound++
    } else {
        Write-Host "  ❌ $func missing in impact_visualizer.js" -ForegroundColor Red
    }
}

Write-Host "Impact Functions Status: $impactFound/$($impactFunctions.Count) functions found" -ForegroundColor Yellow

# Test 4: Check for missing functions in textual_impact_analyzer.js
Write-Host "`nTest 4: Checking textual_impact_analyzer.js functions..." -ForegroundColor Cyan
$textualContent = Get-Content "assets\modules\bugbounty\textual_impact_analyzer.js" -Raw

$textualFunctions = @(
    "analyzeVulnerabilityImpact",
    "generateComprehensiveTextualReport",
    "analyzeBusinessImpact",
    "generateDetailedTextualAnalysis"
)

$textualFound = 0
foreach ($func in $textualFunctions) {
    if ($textualContent -match $func) {
        Write-Host "  ✅ $func found in textual_impact_analyzer.js" -ForegroundColor Green
        $textualFound++
    } else {
        Write-Host "  ❌ $func missing in textual_impact_analyzer.js" -ForegroundColor Red
    }
}

Write-Host "Textual Functions Status: $textualFound/$($textualFunctions.Count) functions found" -ForegroundColor Yellow

# Test 5: Check for file loading in BugBountyCore.js
Write-Host "`nTest 5: Checking file loading implementation..." -ForegroundColor Cyan

$loadingChecks = @(
    "loadAndActivateAllSystemFiles",
    "activateLoadedSystemFiles",
    "useAllComprehensiveSystemFiles",
    "system_config_v4.js",
    "system_verification_v4.js",
    "report_exporter.js",
    "test_system.js"
)

$loadingFound = 0
foreach ($check in $loadingChecks) {
    if ($coreContent -match $check) {
        Write-Host "  ✅ $check found in loading system" -ForegroundColor Green
        $loadingFound++
    } else {
        Write-Host "  ❌ $check missing in loading system" -ForegroundColor Red
    }
}

Write-Host "Loading System Status: $loadingFound/$($loadingChecks.Count) components found" -ForegroundColor Yellow

# Test 6: Check for stack overflow protection
Write-Host "`nTest 6: Checking stack overflow protection..." -ForegroundColor Cyan

$protectionChecks = @(
    "removeCircularReferences",
    "_generatingVulnHTML",
    "_loadingTemplate",
    "STACK OVERFLOW PROTECTION"
)

$protectionFound = 0
foreach ($check in $protectionChecks) {
    if ($coreContent -match $check) {
        Write-Host "  ✅ $check found" -ForegroundColor Green
        $protectionFound++
    } else {
        Write-Host "  ❌ $check missing" -ForegroundColor Red
    }
}

Write-Host "Protection Status: $protectionFound/$($protectionChecks.Count) protections found" -ForegroundColor Yellow

# Test 7: Check template usage
Write-Host "`nTest 7: Checking template usage..." -ForegroundColor Cyan

if (Test-Path "assets\modules\bugbounty\report_template.html") {
    $templateContent = Get-Content "assets\modules\bugbounty\report_template.html" -Raw
    $templateSize = $templateContent.Length
    Write-Host "  ✅ Template file exists ($templateSize chars)" -ForegroundColor Green
    
    if ($coreContent -match "reportTemplateHTML") {
        Write-Host "  ✅ Template loading code found" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Template loading code missing" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ Template file missing" -ForegroundColor Red
}

# Final Summary
Write-Host ""
Write-Host "COMPREHENSIVE SYSTEM STATUS SUMMARY:" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Yellow

$totalScore = $filesExist + $functionsFound + $impactFound + $textualFound + $loadingFound + $protectionFound

Write-Host "Files: $filesExist/$($comprehensiveFiles.Count)" -ForegroundColor White
Write-Host "Core Functions: $functionsFound/$($functions36.Count)" -ForegroundColor White
Write-Host "Impact Functions: $impactFound/$($impactFunctions.Count)" -ForegroundColor White
Write-Host "Textual Functions: $textualFound/$($textualFunctions.Count)" -ForegroundColor White
Write-Host "Loading System: $loadingFound/$($loadingChecks.Count)" -ForegroundColor White
Write-Host "Protection: $protectionFound/$($protectionChecks.Count)" -ForegroundColor White

$maxScore = $comprehensiveFiles.Count + $functions36.Count + $impactFunctions.Count + $textualFunctions.Count + $loadingChecks.Count + $protectionChecks.Count
Write-Host ""
Write-Host "Total Score: $totalScore/$maxScore" -ForegroundColor Yellow

if ($totalScore -ge 35) {
    Write-Host ""
    Write-Host "EXCELLENT: Comprehensive system is working perfectly!" -ForegroundColor Green
    Write-Host "All files and 36 functions are properly implemented" -ForegroundColor Green
} elseif ($totalScore -ge 25) {
    Write-Host ""
    Write-Host "GOOD: Most components are working" -ForegroundColor Yellow
    Write-Host "Some minor issues need attention" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "NEEDS WORK: Major issues found" -ForegroundColor Red
    Write-Host "Significant fixes required" -ForegroundColor Red
}

Write-Host ""
Write-Host "DIRECT POWERSHELL TEST COMPLETED!" -ForegroundColor Green
