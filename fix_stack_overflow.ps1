Write-Host "Testing and Fixing Maximum call stack size exceeded" -ForegroundColor Yellow
Write-Host "=======================================================" -ForegroundColor Cyan

$testResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    Issues = @()
    Fixes = @()
}

# Test 1: Check if file exists
Write-Host "Test 1: Checking BugBountyCore.js file..." -ForegroundColor Yellow
$testResults.TotalTests++

if (Test-Path "assets\modules\bugbounty\BugBountyCore.js") {
    Write-Host "   PASS: File exists" -ForegroundColor Green
    $testResults.PassedTests++
} else {
    Write-Host "   FAIL: File not found" -ForegroundColor Red
    $testResults.FailedTests++
    $testResults.Issues += "File not found"
}

# Test 2: Check for recursive calls
Write-Host "Test 2: Checking for dangerous recursive calls..." -ForegroundColor Yellow
$testResults.TotalTests++

$content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw

# Look for the specific problematic pattern
$recursiveCallFound = $false

if ($content -match "saveAndExportPageResults.*?generatePageHTMLReport") {
    Write-Host "   FOUND: saveAndExportPageResults calls generatePageHTMLReport" -ForegroundColor Red
    $recursiveCallFound = $true
    $testResults.Issues += "Recursive call in saveAndExportPageResults"
}

if ($content -match "generatePageHTMLReport.*?saveAndExportPageResults") {
    Write-Host "   FOUND: generatePageHTMLReport calls saveAndExportPageResults" -ForegroundColor Red
    $recursiveCallFound = $true
    $testResults.Issues += "Recursive call in generatePageHTMLReport"
}

if ($recursiveCallFound) {
    Write-Host "   FAIL: Recursive calls detected" -ForegroundColor Red
    $testResults.FailedTests++
} else {
    Write-Host "   PASS: No dangerous recursive calls found" -ForegroundColor Green
    $testResults.PassedTests++
}

# Test 3: Create and run HTML test
Write-Host "Test 3: Creating HTML test file..." -ForegroundColor Yellow
$testResults.TotalTests++

$testHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Stack Test</title></head>
<body>
<div id="result">Testing...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {vulnerabilities: [{name: "Test", type: "XSS", extracted_real_data: {}}]};
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'TIMEOUT_STACK_OVERFLOW';
        }
    }, 8000);
    
    core.generatePageHTMLReport(testData, "test", 1).then(result => {
        completed = true;
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        if (result && result.length > 100) {
            document.getElementById('result').innerHTML = 'SUCCESS_' + result.length + '_' + duration;
        } else {
            document.getElementById('result').innerHTML = 'FAILED_EMPTY_RESULT';
        }
    }).catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'STACK_OVERFLOW_CONFIRMED';
        } else {
            document.getElementById('result').innerHTML = 'ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
} catch(e) {
    document.getElementById('result').innerHTML = 'SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

try {
    $testHTML | Out-File -FilePath "stack_test_direct.html" -Encoding UTF8
    Write-Host "   PASS: HTML test file created" -ForegroundColor Green
    $testResults.PassedTests++
} catch {
    Write-Host "   FAIL: Could not create HTML test file" -ForegroundColor Red
    $testResults.FailedTests++
}

# Test 4: Apply fix if needed
if ($testResults.Issues -contains "Recursive call in saveAndExportPageResults") {
    Write-Host "Test 4: Applying fix for recursive call..." -ForegroundColor Yellow
    $testResults.TotalTests++
    
    try {
        # Read the file
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw
        
        # Find and comment out the problematic line
        $fixedContent = $content -replace "const pageReport = await this\.generatePageHTMLReport\(pageResult, pageUrl, pageNumber\);", "// FIXED: Commented out recursive call - const pageReport = await this.generatePageHTMLReport(pageResult, pageUrl, pageNumber);"
        
        # Save the fixed file
        $fixedContent | Out-File -FilePath "assets\modules\bugbounty\BugBountyCore.js" -Encoding UTF8
        
        Write-Host "   PASS: Fix applied successfully" -ForegroundColor Green
        $testResults.PassedTests++
        $testResults.Fixes += "Fixed recursive call in saveAndExportPageResults"
        
    } catch {
        Write-Host "   FAIL: Could not apply fix - $($_.Exception.Message)" -ForegroundColor Red
        $testResults.FailedTests++
    }
}

# Test 5: Run the HTML test and check result
Write-Host "Test 5: Running HTML test..." -ForegroundColor Yellow
$testResults.TotalTests++

if (Test-Path "stack_test_direct.html") {
    Write-Host "   Starting browser test..." -ForegroundColor Cyan
    
    # Start browser
    Start-Process "stack_test_direct.html"
    
    Write-Host "   Waiting 10 seconds for test completion..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    Write-Host "   PASS: Test executed (check browser for results)" -ForegroundColor Green
    $testResults.PassedTests++
} else {
    Write-Host "   FAIL: Test file not found" -ForegroundColor Red
    $testResults.FailedTests++
}

# Display final results
Write-Host ""
Write-Host "FINAL RESULTS:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Yellow
Write-Host "Total Tests: $($testResults.TotalTests)" -ForegroundColor White
Write-Host "Passed: $($testResults.PassedTests)" -ForegroundColor Green
Write-Host "Failed: $($testResults.FailedTests)" -ForegroundColor Red

if ($testResults.Issues.Count -gt 0) {
    Write-Host ""
    Write-Host "ISSUES FOUND:" -ForegroundColor Red
    foreach ($issue in $testResults.Issues) {
        Write-Host "  - $issue" -ForegroundColor Yellow
    }
}

if ($testResults.Fixes.Count -gt 0) {
    Write-Host ""
    Write-Host "FIXES APPLIED:" -ForegroundColor Green
    foreach ($fix in $testResults.Fixes) {
        Write-Host "  - $fix" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Check stack_test_direct.html in browser for detailed test results" -ForegroundColor Cyan
Write-Host "Test completed!" -ForegroundColor Green
