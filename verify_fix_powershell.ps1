Write-Host "VERIFYING STACK OVERFLOW FIX" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Yellow

# Create verification test
$verifyHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Stack Fix Verification</title></head>
<body>
<div id="result">Testing fix...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {
        vulnerabilities: [{
            name: "Test SQL Injection After Fix",
            type: "SQL Injection", 
            severity: "Critical",
            description: "Testing after stack overflow fix",
            url: "https://example.com/test",
            parameter: "id",
            payload: "1' OR '1'='1' --",
            evidence: "Login bypass successful",
            extracted_real_data: {}
        }]
    };
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'STILL_TIMEOUT_AFTER_FIX';
        }
    }, 8000);
    
    core.generatePageHTMLReport(testData, "https://example.com/test", 1).then(result => {
        completed = true;
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        if (result && result.length > 100) {
            document.getElementById('result').innerHTML = 'FIX_SUCCESS_' + result.length + '_chars_' + duration + 'ms';
        } else {
            document.getElementById('result').innerHTML = 'FIX_FAILED_EMPTY_RESULT';
        }
    }).catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'FIX_FAILED_STILL_STACK_OVERFLOW';
        } else {
            document.getElementById('result').innerHTML = 'FIX_ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
} catch(e) {
    document.getElementById('result').innerHTML = 'FIX_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

Write-Host "Creating verification test file..." -ForegroundColor Cyan
$verifyHTML | Out-File -FilePath "verify_stack_fix.html" -Encoding UTF8

Write-Host "Starting verification test..." -ForegroundColor Yellow
Start-Process "verify_stack_fix.html"

Write-Host "Waiting 12 seconds for verification..." -ForegroundColor Cyan
Start-Sleep -Seconds 12

Write-Host ""
Write-Host "STACK OVERFLOW FIX VERIFICATION COMPLETED!" -ForegroundColor Green
Write-Host "Check verify_stack_fix.html for results:" -ForegroundColor Cyan
Write-Host "  - FIX_SUCCESS_*: Fix worked successfully" -ForegroundColor Green
Write-Host "  - STILL_TIMEOUT_AFTER_FIX: Fix failed" -ForegroundColor Red
Write-Host "  - FIX_FAILED_STILL_STACK_OVERFLOW: Fix failed" -ForegroundColor Red
