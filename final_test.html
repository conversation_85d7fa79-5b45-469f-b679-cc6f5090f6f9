<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Final Test</title>
</head>
<body>
    <h1>Testing Report Generation</h1>
    <div id="status">Starting test...</div>
    <div id="results"></div>
    
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        async function test() {
            try {
                document.getElementById('status').innerHTML = 'Creating core...';
                const core = new BugBountyCore();
                
                document.getElementById('status').innerHTML = 'Generating report...';
                const testData = [{
                    name: "Test Vulnerability",
                    type: "SQL Injection",
                    severity: "Critical",
                    url: "http://test.com",
                    parameter: "id",
                    payload: "1' OR '1'='1' --",
                    evidence: "SQL injection confirmed",
                    extracted_real_data: {
                        payload: "1' OR '1'='1' --",
                        response: "Database error revealed",
                        evidence: "SQL injection confirmed",
                        url: "http://test.com",
                        parameter: "id"
                    }
                }];
                
                const report = await core.generateVulnerabilitiesHTML(testData);
                
                document.getElementById('status').innerHTML = 'COMPLETED';
                document.getElementById('results').innerHTML = `
                    <h2>RESULTS</h2>
                    <p>Report Length: ${report.length} characters</p>
                    <p>Has comprehensive_details: ${report.includes('comprehensive_details') ? 'YES' : 'NO'}</p>
                    <p>Has exploitation_steps: ${report.includes('exploitation_steps') ? 'YES' : 'NO'}</p>
                    <p>Has dynamic_impact: ${report.includes('dynamic_impact') ? 'YES' : 'NO'}</p>
                    <p>Has visual_impact_data: ${report.includes('visual_impact_data') ? 'YES' : 'NO'}</p>
                    <p>Has textual_impact_analysis: ${report.includes('textual_impact_analysis') ? 'YES' : 'NO'}</p>
                    <p>Has errors: ${report.includes('error') || report.includes('خطأ') ? 'YES' : 'NO'}</p>
                `;
                
                // Download report
                const blob = new Blob([report], {type: 'text/html'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'final_test_report.html';
                a.click();
                
            } catch (error) {
                document.getElementById('status').innerHTML = 'ERROR: ' + error.message;
                document.getElementById('results').innerHTML = 'Error: ' + error.message;
            }
        }
        
        test();
    </script>
</body>
</html>
