Write-Host "DEBUGGING COMPREHENSIVE OUTPUT FROM 36 FUNCTIONS" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Yellow

# Create debug test with console logging
$debugTestHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Debug Comprehensive Output</title></head>
<body>
<div id="result">Debugging comprehensive output...</div>
<div id="console-output" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
// Capture console.log output
const originalLog = console.log;
const consoleOutput = document.getElementById('console-output');
console.log = function(...args) {
    originalLog.apply(console, args);
    consoleOutput.textContent += args.join(' ') + '\n';
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
};

try {
    const core = new BugBountyCore();
    
    // Create test data with extracted_real_data
    const testData = [{
        name: "API Authentication Bypass Debug Test",
        type: "API Authentication Bypass", 
        severity: "Critical",
        description: "تم اكتشاف ثغرة تجاوز المصادقة في API - اختبار تشخيصي",
        url: "http://testphp.vulnweb.com/api/login",
        parameter: "token",
        payload: "admin' OR '1'='1' --",
        evidence: "تم تجاوز المصادقة بنجاح",
        method: "POST",
        response: "Authentication bypassed successfully",
        confidence_level: 95,
        extracted_real_data: {
            payload: "admin' OR '1'='1' --",
            response: "Authentication bypassed successfully", 
            evidence: "تم تجاوز المصادقة بنجاح",
            url: "http://testphp.vulnweb.com/api/login",
            parameter: "token",
            method: "POST",
            status_code: 200,
            response_time: 150,
            headers: {"Content-Type": "application/json"},
            body: '{"success": true, "user": "admin"}',
            exploitation_confirmed: true,
            vulnerability_confirmed: true,
            testing_results: {
                successful: true,
                payload_executed: true,
                response_indicates_success: true
            }
        }
    }];
    
    console.log('🚀 بدء اختبار تشخيصي للدوال الـ 36');
    console.log('📋 بيانات الاختبار:', JSON.stringify(testData[0], null, 2));
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'DEBUG_TEST_TIMEOUT';
        }
    }, 25000);
    
    core.generateVulnerabilitiesHTML(testData)
        .then(result => {
            completed = true;
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            console.log('📊 نتائج الاختبار التشخيصي:');
            console.log('- طول النتيجة:', result.length);
            console.log('- وقت التنفيذ:', duration + 'ms');
            console.log('- أول 500 حرف:', result.substring(0, 500));
            console.log('- آخر 500 حرف:', result.substring(Math.max(0, result.length - 500)));
            
            if (result && result.length > 2000) {
                // Check for specific content
                const hasRealDetails = result.includes('التفاصيل التقنية');
                const hasImpactAnalysis = result.includes('تحليل التأثير');
                const hasExploitationResults = result.includes('نتائج الاستغلال');
                const hasComprehensiveContent = result.includes('comprehensive_details');
                const hasGeneratedContent = !result.includes('لم يتم إنتاج');
                
                console.log('✅ فحص المحتوى:');
                console.log('- التفاصيل التقنية:', hasRealDetails);
                console.log('- تحليل التأثير:', hasImpactAnalysis);
                console.log('- نتائج الاستغلال:', hasExploitationResults);
                console.log('- محتوى شامل:', hasComprehensiveContent);
                console.log('- محتوى مُنتج:', hasGeneratedContent);
                
                const score = [hasRealDetails, hasImpactAnalysis, hasExploitationResults, hasComprehensiveContent, hasGeneratedContent].filter(Boolean).length;
                
                document.getElementById('result').innerHTML = 
                    'DEBUG_SUCCESS_' + 
                    result.length + '_chars_' + 
                    duration + 'ms_' + 
                    score + '_of_5_checks_passed';
            } else {
                console.log('❌ النتيجة قصيرة جداً');
                document.getElementById('result').innerHTML = 'DEBUG_FAILED_SHORT_RESULT_' + result.length;
            }
        })
        .catch(err => {
            completed = true;
            clearTimeout(timeout);
            console.log('❌ خطأ في الاختبار:', err.message);
            console.log('❌ تفاصيل الخطأ:', err.stack);
            document.getElementById('result').innerHTML = 'DEBUG_ERROR_' + err.message.replace(/\s+/g, '_');
        });
    
} catch(e) {
    console.log('❌ خطأ في السكريبت:', e.message);
    console.log('❌ تفاصيل الخطأ:', e.stack);
    document.getElementById('result').innerHTML = 'DEBUG_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

Write-Host "Creating debug comprehensive output test..." -ForegroundColor Cyan
$debugTestHTML | Out-File -FilePath "debug_comprehensive_output_test.html" -Encoding UTF8

Write-Host "Starting debug comprehensive output test..." -ForegroundColor Yellow
Start-Process "debug_comprehensive_output_test.html"

Write-Host "Waiting 30 seconds for debug test..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "DEBUG COMPREHENSIVE OUTPUT TEST COMPLETED!" -ForegroundColor Green
Write-Host "Check debug_comprehensive_output_test.html for detailed console output" -ForegroundColor Cyan
