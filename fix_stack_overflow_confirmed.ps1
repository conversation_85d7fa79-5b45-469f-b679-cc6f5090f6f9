Write-Host "FIXING CONFIRMED Maximum call stack size exceeded" -ForegroundColor Red
Write-Host "=================================================" -ForegroundColor Yellow

$fixResults = @{
    TotalFixes = 0
    AppliedFixes = 0
    FailedFixes = 0
    Details = @()
}

# Read the current file
Write-Host "Reading BugBountyCore.js..." -ForegroundColor Cyan
$content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -Raw

# Fix 1: Find and fix the recursive call in saveAndExportPageResults
Write-Host "Fix 1: Searching for recursive call in saveAndExportPageResults..." -ForegroundColor Yellow
$fixResults.TotalFixes++

$pattern1 = "const pageReport = await this\.generatePageHTMLReport\(pageResult, pageUrl, pageNumber\);"
if ($content -match $pattern1) {
    Write-Host "   FOUND: Recursive call in saveAndExportPageResults" -ForegroundColor Red
    
    # Replace with a simple HTML template instead of calling the function
    $replacement1 = @"
// FIXED: Replaced recursive call with direct HTML generation
                const pageReport = ``<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير Bug Bounty - الصفحة `${pageNumber}</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
        .vuln { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ تقرير Bug Bounty</h1>
        <p>الصفحة: `${pageNumber} | الرابط: `${pageUrl}</p>
        <p>تم الإنشاء: `${new Date().toLocaleString('ar')}</p>
    </div>
    `${pageResult.vulnerabilities ? pageResult.vulnerabilities.map(v => ``
    <div class="vuln">
        <h3>🚨 `${v.name || 'ثغرة مكتشفة'}</h3>
        <p><strong>النوع:</strong> `${v.type || 'غير محدد'}</p>
        <p><strong>الخطورة:</strong> `${v.severity || 'متوسط'}</p>
        <p><strong>الوصف:</strong> `${v.description || 'تم اكتشاف ثغرة أمنية'}</p>
    </div>
    ``).join('') : '<p>لا توجد ثغرات</p>'}
</body>
</html>``;
"@
    
    $content = $content -replace [regex]::Escape($pattern1), $replacement1
    Write-Host "   FIXED: Replaced recursive call with direct HTML generation" -ForegroundColor Green
    $fixResults.AppliedFixes++
    $fixResults.Details += "Fixed recursive call in saveAndExportPageResults"
} else {
    Write-Host "   NOT FOUND: Pattern not found in saveAndExportPageResults" -ForegroundColor Yellow
}

# Fix 2: Add protection to generatePageHTMLReport
Write-Host "Fix 2: Adding stack overflow protection to generatePageHTMLReport..." -ForegroundColor Yellow
$fixResults.TotalFixes++

$pattern2 = "async generatePageHTMLReport\(pageResult, pageUrl, pageNumber\) \{"
if ($content -match $pattern2) {
    Write-Host "   FOUND: generatePageHTMLReport function" -ForegroundColor Green
    
    $replacement2 = @"
async generatePageHTMLReport(pageResult, pageUrl, pageNumber) {
        // STACK OVERFLOW PROTECTION
        if (!this._callStack) this._callStack = new Set();
        const callKey = ``generatePageHTMLReport_`${pageUrl}_`${pageNumber}``;
        if (this._callStack.has(callKey)) {
            console.warn('Stack overflow protection: Preventing recursive call');
            return '<html><body><h1>Stack overflow prevented</h1></body></html>';
        }
        this._callStack.add(callKey);
        
        try {
"@
    
    $content = $content -replace [regex]::Escape("async generatePageHTMLReport(pageResult, pageUrl, pageNumber) {"), $replacement2
    Write-Host "   FIXED: Added stack overflow protection" -ForegroundColor Green
    $fixResults.AppliedFixes++
    $fixResults.Details += "Added stack overflow protection to generatePageHTMLReport"
} else {
    Write-Host "   NOT FOUND: generatePageHTMLReport function signature not found" -ForegroundColor Yellow
}

# Fix 3: Add cleanup in finally block
Write-Host "Fix 3: Adding cleanup in generatePageHTMLReport..." -ForegroundColor Yellow
$fixResults.TotalFixes++

# Find the end of generatePageHTMLReport function and add finally block
$pattern3 = "console\.log\(`✅ تم إنشاء تقرير منفصل للصفحة \$\{pageNumber\} بنجاح`\);\s*return finalReport;"
if ($content -match $pattern3) {
    Write-Host "   FOUND: End of generatePageHTMLReport function" -ForegroundColor Green
    
    $replacement3 = @"
console.log(`✅ تم إنشاء تقرير منفصل للصفحة $${pageNumber} بنجاح`);
            return finalReport;
        } finally {
            // CLEANUP: Remove from call stack
            if (this._callStack) {
                this._callStack.delete(callKey);
            }
        }
"@
    
    $content = $content -replace $pattern3, $replacement3
    Write-Host "   FIXED: Added cleanup in finally block" -ForegroundColor Green
    $fixResults.AppliedFixes++
    $fixResults.Details += "Added cleanup in finally block"
} else {
    Write-Host "   NOT FOUND: End pattern not found" -ForegroundColor Yellow
}

# Save the fixed file
Write-Host "Saving fixed file..." -ForegroundColor Cyan
try {
    $content | Out-File -FilePath "assets\modules\bugbounty\BugBountyCore.js" -Encoding UTF8
    Write-Host "   SUCCESS: File saved" -ForegroundColor Green
} catch {
    Write-Host "   ERROR: Could not save file - $($_.Exception.Message)" -ForegroundColor Red
    $fixResults.FailedFixes++
}

# Create verification test
Write-Host "Creating verification test..." -ForegroundColor Cyan
$verifyHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Stack Fix Verification</title></head>
<body>
<div id="result">Testing fix...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {vulnerabilities: [{name: "Test After Fix", type: "XSS", extracted_real_data: {}}]};
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'STILL_TIMEOUT_AFTER_FIX';
        }
    }, 8000);
    
    core.generatePageHTMLReport(testData, "test", 1).then(result => {
        completed = true;
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        if (result && result.length > 100) {
            document.getElementById('result').innerHTML = 'FIX_SUCCESS_' + result.length + '_' + duration + 'ms';
        } else {
            document.getElementById('result').innerHTML = 'FIX_FAILED_EMPTY_RESULT';
        }
    }).catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'FIX_FAILED_STILL_STACK_OVERFLOW';
        } else {
            document.getElementById('result').innerHTML = 'FIX_ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
} catch(e) {
    document.getElementById('result').innerHTML = 'FIX_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

$verifyHTML | Out-File -FilePath "verify_stack_fix.html" -Encoding UTF8

# Display results
Write-Host ""
Write-Host "FIX RESULTS:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Yellow
Write-Host "Total Fixes Attempted: $($fixResults.TotalFixes)" -ForegroundColor White
Write-Host "Successfully Applied: $($fixResults.AppliedFixes)" -ForegroundColor Green
Write-Host "Failed: $($fixResults.FailedFixes)" -ForegroundColor Red

if ($fixResults.Details.Count -gt 0) {
    Write-Host ""
    Write-Host "APPLIED FIXES:" -ForegroundColor Green
    foreach ($detail in $fixResults.Details) {
        Write-Host "  ✅ $detail" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Starting verification test..." -ForegroundColor Yellow
Start-Process "verify_stack_fix.html"

Write-Host "Waiting 10 seconds for verification..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

Write-Host ""
Write-Host "STACK OVERFLOW FIX COMPLETED!" -ForegroundColor Green
Write-Host "Check verify_stack_fix.html for verification results" -ForegroundColor Cyan
