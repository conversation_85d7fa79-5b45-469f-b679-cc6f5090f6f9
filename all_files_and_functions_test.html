﻿<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>All Files and Functions Test</title></head>
<body>
<div id="result">Testing all files and 36 functions...</div>
<div id="console-output" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 600px; overflow-y: auto;"></div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
// Capture console.log output
const originalLog = console.log;
const consoleOutput = document.getElementById('console-output');
console.log = function(...args) {
    originalLog.apply(console, args);
    consoleOutput.textContent += args.join(' ') + '\n';
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
};

try {
    const core = new BugBountyCore();
    
    // Create comprehensive test data
    const testData = [{
        name: "Ultimate All Files and Functions Test",
        type: "API Authentication Bypass", 
        severity: "Critical",
        description: "ط§ط®طھط¨ط§ط± ط´ط§ظ…ظ„ ظ„ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ ظˆط§ظ„ط¯ظˆط§ظ„ ط§ظ„ظ€ 36 ظپظٹ ط§ظ„ظ†ط¸ط§ظ… v4.0",
        url: "http://testphp.vulnweb.com/api/login",
        parameter: "token",
        payload: "admin' OR '1'='1' --",
        evidence: "طھظ… طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ط¨ظ†ط¬ط§ط­ - ط§ط®طھط¨ط§ط± ط´ط§ظ…ظ„ ظ„ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ",
        method: "POST",
        response: "Authentication bypassed successfully - All Files Test",
        confidence_level: 95,
        extracted_real_data: {
            payload: "admin' OR '1'='1' --",
            response: "Authentication bypassed successfully - All Files Test", 
            evidence: "طھظ… طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ط¨ظ†ط¬ط§ط­ - ط§ط®طھط¨ط§ط± ط´ط§ظ…ظ„ ظ„ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ",
            url: "http://testphp.vulnweb.com/api/login",
            parameter: "token",
            method: "POST",
            status_code: 200,
            response_time: 150,
            headers: {"Content-Type": "application/json"},
            body: '{"success": true, "user": "admin", "all_files_test": true}',
            exploitation_confirmed: true,
            vulnerability_confirmed: true,
            testing_results: {
                successful: true,
                payload_executed: true,
                response_indicates_success: true,
                all_files_test: true
            }
        }
    }];
    
    console.log('ًںڑ€ ط¨ط¯ط، ط§ظ„ط§ط®طھط¨ط§ط± ط§ظ„ط´ط§ظ…ظ„ ظ„ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ ظˆط§ظ„ط¯ظˆط§ظ„ ط§ظ„ظ€ 36');
    console.log('ًں“‹ ط¨ظٹط§ظ†ط§طھ ط§ظ„ط§ط®طھط¨ط§ط± ط§ظ„ط´ط§ظ…ظ„:', JSON.stringify(testData[0], null, 2));
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_TEST_TIMEOUT';
        }
    }, 50000);
    
    core.generateVulnerabilitiesHTML(testData)
        .then(result => {
            completed = true;
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            console.log('ًں“ٹ ظ†طھط§ط¦ط¬ ط§ظ„ط§ط®طھط¨ط§ط± ط§ظ„ط´ط§ظ…ظ„ ظ„ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ ظˆط§ظ„ط¯ظˆط§ظ„:');
            console.log('- ط·ظˆظ„ ط§ظ„ظ†طھظٹط¬ط©:', result.length);
            console.log('- ظˆظ‚طھ ط§ظ„طھظ†ظپظٹط°:', duration + 'ms');
            console.log('- ط£ظˆظ„ 2000 ط­ط±ظپ:', result.substring(0, 2000));
            
            if (result && result.length > 15000) {
                // Check for all comprehensive content from 36 functions
                const has36Functions = [
                    result.includes('comprehensive_details') || result.includes('ط§ظ„طھظپط§طµظٹظ„ ط§ظ„ط´ط§ظ…ظ„ط©'),
                    result.includes('exploitation_steps') || result.includes('ط®ط·ظˆط§طھ ط§ظ„ط§ط³طھط؛ظ„ط§ظ„'),
                    result.includes('dynamic_impact') || result.includes('طھط­ظ„ظٹظ„ ط§ظ„طھط£ط«ظٹط±'),
                    result.includes('visual_impact_data') || result.includes('ط§ظ„طھطµظˆط±ط§طھ ط§ظ„ط¨طµط±ظٹط©'),
                    result.includes('textual_impact_analysis') || result.includes('ط§ظ„طھط­ظ„ظٹظ„ ط§ظ„ظ†طµظٹ'),
                    result.includes('security_impact_analysis') || result.includes('طھط­ظ„ظٹظ„ ط§ظ„طھط£ط«ظٹط± ط§ظ„ط£ظ…ظ†ظٹ'),
                    result.includes('risk_analysis') || result.includes('طھط­ظ„ظٹظ„ ط§ظ„ظ…ط®ط§ط·ط±'),
                    result.includes('threat_modeling') || result.includes('ظ†ظ…ط°ط¬ط© ط§ظ„طھظ‡ط¯ظٹط¯ط§طھ'),
                    result.includes('business_impact_analysis') || result.includes('ط§ظ„طھط£ط«ظٹط± ط§ظ„طھط¬ط§ط±ظٹ'),
                    result.includes('comprehensive_textual_report') || result.includes('ط§ظ„طھظ‚ط±ظٹط± ط§ظ„ظ†طµظٹ ط§ظ„ط´ط§ظ…ظ„'),
                    result.includes('detailed_textual_analysis') || result.includes('ط§ظ„طھط­ظ„ظٹظ„ ط§ظ„ظ†طµظٹ ط§ظ„ظ…ظپطµظ„')
                ].filter(Boolean).length;
                
                // Check for all comprehensive files
                const hasAllFiles = [
                    result.includes('impact_visualizer.js') || result.includes('ط§ظ„طھطµظˆط±ط§طھ ط§ظ„ط¨طµط±ظٹط©'),
                    result.includes('textual_impact_analyzer.js') || result.includes('ط§ظ„طھط­ظ„ظٹظ„ ط§ظ„ظ†طµظٹ'),
                    result.includes('SystemConfigV4') || result.includes('طھط­ظ„ظٹظ„ طھظƒظˆظٹظ† ط§ظ„ظ†ط¸ط§ظ…'),
                    result.includes('SystemVerificationV4') || result.includes('ظ†طھط§ط¦ط¬ ط§ظ„طھط­ظ‚ظ‚'),
                    result.includes('ReportExporter') || result.includes('طھظ†ط³ظٹظ‚ط§طھ ط§ظ„طھطµط¯ظٹط±'),
                    result.includes('TestSystem') || result.includes('ظ†طھط§ط¦ط¬ ط§ظ„ط§ط®طھط¨ط§ط±'),
                    result.includes('PythonScreenshotBridge') || result.includes('طھط­ظ„ظٹظ„ Python')
                ].filter(Boolean).length;
                
                const hasRealContent = !result.includes('ظ„ظ… ظٹطھظ… ط¥ظ†طھط§ط¬');
                const hasNoErrors = !result.includes('ط®ط·ط£ ظپظٹ ط¥ظ†ط´ط§ط، ط§ظ„طھظ‚ط±ظٹط±');
                
                console.log('âœ… ظپط­طµ ط§ظ„ظ…ط­طھظˆظ‰ ط§ظ„ط´ط§ظ…ظ„ ظ„ط¬ظ…ظٹط¹ ط§ظ„ظ…ظ„ظپط§طھ ظˆط§ظ„ط¯ظˆط§ظ„:');
                console.log('- ط§ظ„ط¯ظˆط§ظ„ ط§ظ„ظ€ 36:', has36Functions, 'ظ…ظ† 11');
                console.log('- ط§ظ„ظ…ظ„ظپط§طھ ط§ظ„ط´ط§ظ…ظ„ط©:', hasAllFiles, 'ظ…ظ† 7');
                console.log('- ظ…ط­طھظˆظ‰ ط­ظ‚ظٹظ‚ظٹ:', hasRealContent);
                console.log('- ط¨ط¯ظˆظ† ط£ط®ط·ط§ط،:', hasNoErrors);
                
                const totalScore = has36Functions + hasAllFiles + (hasRealContent ? 1 : 0) + (hasNoErrors ? 1 : 0);
                
                if (totalScore >= 16) {
                    document.getElementById('result').innerHTML = 
                        'ALL_FILES_FUNCTIONS_SUCCESS_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        has36Functions + '_functions_' +
                        hasAllFiles + '_files_' +
                        'PERFECT_COMPREHENSIVE_SYSTEM';
                } else if (totalScore >= 12) {
                    document.getElementById('result').innerHTML = 
                        'ALL_FILES_FUNCTIONS_GOOD_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        has36Functions + '_functions_' +
                        hasAllFiles + '_files_' +
                        'MOST_WORKING';
                } else {
                    document.getElementById('result').innerHTML = 
                        'ALL_FILES_FUNCTIONS_PARTIAL_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        has36Functions + '_functions_' +
                        hasAllFiles + '_files_' +
                        'SOME_WORKING';
                }
            } else {
                console.log('â‌Œ ط§ظ„ظ†طھظٹط¬ط© ظ‚طµظٹط±ط© ط¬ط¯ط§ظ‹');
                document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_FAILED_SHORT_RESULT_' + result.length;
            }
        })
        .catch(err => {
            completed = true;
            clearTimeout(timeout);
            console.log('â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„ط§ط®طھط¨ط§ط± ط§ظ„ط´ط§ظ…ظ„:', err.message);
            console.log('â‌Œ طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£:', err.stack);
            document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_ERROR_' + err.message.replace(/\s+/g, '_');
        });
    
} catch(e) {
    console.log('â‌Œ ط®ط·ط£ ظپظٹ ط§ظ„ط³ظƒط±ظٹط¨طھ ط§ظ„ط´ط§ظ…ظ„:', e.message);
    console.log('â‌Œ طھظپط§طµظٹظ„ ط§ظ„ط®ط·ط£:', e.stack);
    document.getElementById('result').innerHTML = 'ALL_FILES_FUNCTIONS_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
