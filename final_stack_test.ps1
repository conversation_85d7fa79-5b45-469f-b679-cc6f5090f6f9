Write-Host "FINAL STACK OVERFLOW FIX TEST" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Yellow

# Create comprehensive test
$finalTestHTML = @"
<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Final Stack Test</title></head>
<body>
<div id="result">Testing comprehensive fix...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {
        vulnerabilities: [{
            name: "Final Test SQL Injection",
            type: "SQL Injection", 
            severity: "Critical",
            description: "Final comprehensive test after all fixes",
            url: "https://example.com/final-test",
            parameter: "id",
            payload: "1' UNION SELECT * FROM users --",
            evidence: "Database access confirmed",
            extracted_real_data: {
                payload: "1' UNION SELECT * FROM users --",
                response: "User data exposed",
                evidence: "Full database dump"
            }
        }, {
            name: "Final Test XSS",
            type: "Cross-Site Scripting",
            severity: "High", 
            description: "XSS test for comprehensive verification",
            url: "https://example.com/search",
            parameter: "q",
            payload: "<script>alert('XSS')</script>",
            evidence: "Script executed successfully"
        }]
    };
    
    const startTime = Date.now();
    let completed = false;
    let testCount = 0;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'FINAL_TEST_TIMEOUT_STILL_FAILING';
        }
    }, 10000);
    
    // Test multiple calls to ensure no recursion
    function runTest() {
        testCount++;
        console.log('Running test', testCount);
        
        return core.generatePageHTMLReport(testData, "https://example.com/final-test", testCount)
            .then(result => {
                const duration = Date.now() - startTime;
                if (result && result.length > 100) {
                    if (testCount < 3) {
                        // Run another test to ensure no recursion
                        return runTest();
                    } else {
                        completed = true;
                        clearTimeout(timeout);
                        document.getElementById('result').innerHTML = 
                            'FINAL_SUCCESS_ALL_TESTS_PASSED_' + 
                            result.length + '_chars_' + 
                            duration + 'ms_' + 
                            testCount + '_tests';
                        return result;
                    }
                } else {
                    throw new Error('Empty result');
                }
            });
    }
    
    runTest().catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'FINAL_FAILED_STACK_OVERFLOW_PERSISTS';
        } else {
            document.getElementById('result').innerHTML = 'FINAL_ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
    
} catch(e) {
    document.getElementById('result').innerHTML = 'FINAL_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
"@

Write-Host "Creating final comprehensive test..." -ForegroundColor Cyan
$finalTestHTML | Out-File -FilePath "final_stack_test.html" -Encoding UTF8

Write-Host "Starting final test..." -ForegroundColor Yellow
Start-Process "final_stack_test.html"

Write-Host "Waiting 15 seconds for comprehensive test..." -ForegroundColor Cyan
Start-Sleep -Seconds 15

Write-Host ""
Write-Host "FINAL TEST RESULTS:" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Yellow
Write-Host "Check final_stack_test.html for results:" -ForegroundColor Cyan
Write-Host "  - FINAL_SUCCESS_ALL_TESTS_PASSED: Complete fix success" -ForegroundColor Green
Write-Host "  - FINAL_TEST_TIMEOUT_STILL_FAILING: Fix failed" -ForegroundColor Red
Write-Host "  - FINAL_FAILED_STACK_OVERFLOW_PERSISTS: Stack overflow still exists" -ForegroundColor Red

Write-Host ""
Write-Host "COMPREHENSIVE STACK OVERFLOW FIX TEST COMPLETED!" -ForegroundColor Green
