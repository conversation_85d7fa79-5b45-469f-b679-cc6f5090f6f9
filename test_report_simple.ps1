Write-Host "TESTING REAL COMPREHENSIVE REPORT GENERATION" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Yellow

Write-Host "Starting HTML test..." -ForegroundColor Cyan
Start-Process "test_real_report_direct.html"

Write-Host "Waiting 30 seconds for test completion..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "CHECKING RESULTS..." -ForegroundColor Cyan

if (Test-Path "real_report_test_results.json") {
    Write-Host "Test results found!" -ForegroundColor Green
    
    $results = Get-Content "real_report_test_results.json" | ConvertFrom-Json
    
    Write-Host ""
    Write-Host "RESULTS:" -ForegroundColor Yellow
    Write-Host "Status: $($results.status)" -ForegroundColor Green
    Write-Host "Report Length: $($results.reportLength) characters" -ForegroundColor White
    Write-Host "Generation Time: $($results.generationTime)ms" -ForegroundColor White
    Write-Host "Functions Score: $($results.functionsScore)/11" -ForegroundColor White
    Write-Host "Files Score: $($results.filesScore)/7" -ForegroundColor White
    Write-Host "Quality Score: $($results.qualityScore)/6" -ForegroundColor White
    Write-Host "Total Score: $($results.totalScore)/24 ($($results.percentage)%)" -ForegroundColor White
    
    Write-Host ""
    Write-Host "FUNCTIONS ANALYSIS:" -ForegroundColor Cyan
    if ($results.analysis.hasComprehensiveDetails) { Write-Host "✅ Comprehensive Details" -ForegroundColor Green } else { Write-Host "❌ Comprehensive Details" -ForegroundColor Red }
    if ($results.analysis.hasExploitationSteps) { Write-Host "✅ Exploitation Steps" -ForegroundColor Green } else { Write-Host "❌ Exploitation Steps" -ForegroundColor Red }
    if ($results.analysis.hasDynamicImpact) { Write-Host "✅ Dynamic Impact" -ForegroundColor Green } else { Write-Host "❌ Dynamic Impact" -ForegroundColor Red }
    if ($results.analysis.hasVisualAnalysis) { Write-Host "✅ Visual Analysis" -ForegroundColor Green } else { Write-Host "❌ Visual Analysis" -ForegroundColor Red }
    if ($results.analysis.hasTextualAnalysis) { Write-Host "✅ Textual Analysis" -ForegroundColor Green } else { Write-Host "❌ Textual Analysis" -ForegroundColor Red }
    if ($results.analysis.hasSecurityAnalysis) { Write-Host "✅ Security Analysis" -ForegroundColor Green } else { Write-Host "❌ Security Analysis" -ForegroundColor Red }
    if ($results.analysis.hasRiskAnalysis) { Write-Host "✅ Risk Analysis" -ForegroundColor Green } else { Write-Host "❌ Risk Analysis" -ForegroundColor Red }
    if ($results.analysis.hasThreatModeling) { Write-Host "✅ Threat Modeling" -ForegroundColor Green } else { Write-Host "❌ Threat Modeling" -ForegroundColor Red }
    if ($results.analysis.hasBusinessImpact) { Write-Host "✅ Business Impact" -ForegroundColor Green } else { Write-Host "❌ Business Impact" -ForegroundColor Red }
    
    Write-Host ""
    Write-Host "FILES ANALYSIS:" -ForegroundColor Cyan
    if ($results.analysis.hasSystemConfigV4) { Write-Host "✅ SystemConfigV4" -ForegroundColor Green } else { Write-Host "❌ SystemConfigV4" -ForegroundColor Red }
    if ($results.analysis.hasSystemVerificationV4) { Write-Host "✅ SystemVerificationV4" -ForegroundColor Green } else { Write-Host "❌ SystemVerificationV4" -ForegroundColor Red }
    if ($results.analysis.hasReportExporter) { Write-Host "✅ ReportExporter" -ForegroundColor Green } else { Write-Host "❌ ReportExporter" -ForegroundColor Red }
    if ($results.analysis.hasTestSystem) { Write-Host "✅ TestSystem" -ForegroundColor Green } else { Write-Host "❌ TestSystem" -ForegroundColor Red }
    if ($results.analysis.hasPythonScreenshotBridge) { Write-Host "✅ PythonScreenshotBridge" -ForegroundColor Green } else { Write-Host "❌ PythonScreenshotBridge" -ForegroundColor Red }
    if ($results.analysis.hasImpactVisualizerData) { Write-Host "✅ Impact Visualizer" -ForegroundColor Green } else { Write-Host "❌ Impact Visualizer" -ForegroundColor Red }
    if ($results.analysis.hasTextualImpactAnalyzerData) { Write-Host "✅ Textual Impact Analyzer" -ForegroundColor Green } else { Write-Host "❌ Textual Impact Analyzer" -ForegroundColor Red }
    
    Write-Host ""
    Write-Host "QUALITY ANALYSIS:" -ForegroundColor Cyan
    if ($results.analysis.hasNoErrors) { Write-Host "✅ No Errors" -ForegroundColor Green } else { Write-Host "❌ Has Errors" -ForegroundColor Red }
    if ($results.analysis.hasNoDefaultContent) { Write-Host "✅ No Default Content" -ForegroundColor Green } else { Write-Host "❌ Has Default Content" -ForegroundColor Red }
    if ($results.analysis.hasRealContent) { Write-Host "✅ Real Content (>10KB)" -ForegroundColor Green } else { Write-Host "❌ Small Content (<10KB)" -ForegroundColor Red }
    if ($results.analysis.hasHTMLStructure) { Write-Host "✅ HTML Structure" -ForegroundColor Green } else { Write-Host "❌ No HTML Structure" -ForegroundColor Red }
    
    Write-Host ""
    Write-Host "FINAL ASSESSMENT:" -ForegroundColor Yellow
    if ($results.percentage -ge 90) {
        Write-Host "EXCELLENT: Perfect comprehensive system!" -ForegroundColor Green
    } elseif ($results.percentage -ge 75) {
        Write-Host "VERY GOOD: Most features working!" -ForegroundColor Green
    } elseif ($results.percentage -ge 60) {
        Write-Host "GOOD: Basic features working" -ForegroundColor Yellow
    } else {
        Write-Host "NEEDS WORK: Major issues" -ForegroundColor Red
    }
    
} elseif (Test-Path "real_report_test_error.json") {
    Write-Host "Test encountered an error!" -ForegroundColor Red
    $errorResults = Get-Content "real_report_test_error.json" | ConvertFrom-Json
    Write-Host "Error: $($errorResults.error)" -ForegroundColor Red
} else {
    Write-Host "No test results found!" -ForegroundColor Red
}

Write-Host ""
if (Test-Path "generated_comprehensive_report.html") {
    $reportSize = (Get-Item "generated_comprehensive_report.html").Length
    Write-Host "COMPREHENSIVE REPORT GENERATED!" -ForegroundColor Green
    Write-Host "Report size: $reportSize bytes" -ForegroundColor White
    
    if ($reportSize -gt 20000) {
        Write-Host "EXCELLENT: Very comprehensive report!" -ForegroundColor Green
    } elseif ($reportSize -gt 10000) {
        Write-Host "GOOD: Substantial report!" -ForegroundColor Yellow
    } else {
        Write-Host "BASIC: Small report" -ForegroundColor Yellow
    }
} else {
    Write-Host "NO REPORT GENERATED!" -ForegroundColor Red
}

Write-Host ""
Write-Host "TEST COMPLETED!" -ForegroundColor Green
