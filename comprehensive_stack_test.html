﻿<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Comprehensive Stack Fix Test</title></head>
<body>
<div id="result">Testing comprehensive fix...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    
    // Create test data with potential circular references
    const testData = [{
        name: "Comprehensive Test SQL Injection",
        type: "SQL Injection", 
        severity: "Critical",
        description: "Testing comprehensive stack overflow fix",
        url: "https://example.com/test",
        parameter: "id",
        payload: "1' UNION SELECT * FROM users --",
        evidence: "Database access confirmed",
        extracted_real_data: {}
    }];
    
    // Add potential circular reference
    testData[0].self_ref = testData[0];
    testData[0].parent = testData;
    
    const startTime = Date.now();
    let completed = false;
    let testCount = 0;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_TEST_TIMEOUT_FAILED';
        }
    }, 12000);
    
    function runComprehensiveTest() {
        testCount++;
        console.log('Running comprehensive test', testCount);
        
        return core.generateVulnerabilitiesHTML(testData)
            .then(result => {
                const duration = Date.now() - startTime;
                if (result && result.length > 100) {
                    if (testCount < 2) {
                        // Run another test to ensure stability
                        return runComprehensiveTest();
                    } else {
                        completed = true;
                        clearTimeout(timeout);
                        document.getElementById('result').innerHTML = 
                            'COMPREHENSIVE_SUCCESS_' + 
                            result.length + '_chars_' + 
                            duration + 'ms_' + 
                            testCount + '_tests_CIRCULAR_REFS_HANDLED';
                        return result;
                    }
                } else {
                    throw new Error('Empty or invalid result');
                }
            });
    }
    
    runComprehensiveTest().catch(err => {
        completed = true;
        clearTimeout(timeout);
        if (err.message.includes('Maximum call stack size exceeded')) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_FAILED_STACK_OVERFLOW_PERSISTS';
        } else if (err.message.includes('Circular')) {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_FAILED_CIRCULAR_REFS_NOT_HANDLED';
        } else {
            document.getElementById('result').innerHTML = 'COMPREHENSIVE_ERROR_' + err.message.replace(/\s+/g, '_');
        }
    });
    
} catch(e) {
    document.getElementById('result').innerHTML = 'COMPREHENSIVE_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
