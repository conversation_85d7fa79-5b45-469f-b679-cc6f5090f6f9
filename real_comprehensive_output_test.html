﻿<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Real Comprehensive Output Test</title></head>
<body>
<div id="result">Testing real comprehensive output...</div>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    
    // Create test data with extracted_real_data to trigger comprehensive functions
    const testData = [{
        name: "API Authentication Bypass",
        type: "API Authentication Bypass", 
        severity: "Critical",
        description: "طھظ… ط§ظƒطھط´ط§ظپ ط«ط؛ط±ط© طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ظپظٹ API",
        url: "http://testphp.vulnweb.com/api/login",
        parameter: "token",
        payload: "admin' OR '1'='1' --",
        evidence: "طھظ… طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ط¨ظ†ط¬ط§ط­",
        method: "POST",
        response: "Authentication bypassed successfully",
        confidence_level: 95,
        extracted_real_data: {
            payload: "admin' OR '1'='1' --",
            response: "Authentication bypassed successfully", 
            evidence: "طھظ… طھط¬ط§ظˆط² ط§ظ„ظ…طµط§ط¯ظ‚ط© ط¨ظ†ط¬ط§ط­",
            url: "http://testphp.vulnweb.com/api/login",
            parameter: "token",
            method: "POST",
            status_code: 200,
            response_time: 150,
            headers: {"Content-Type": "application/json"},
            body: '{"success": true, "user": "admin"}',
            exploitation_confirmed: true,
            vulnerability_confirmed: true,
            testing_results: {
                successful: true,
                payload_executed: true,
                response_indicates_success: true
            }
        }
    }];
    
    const startTime = Date.now();
    let completed = false;
    
    const timeout = setTimeout(() => {
        if (!completed) {
            document.getElementById('result').innerHTML = 'REAL_OUTPUT_TEST_TIMEOUT';
        }
    }, 20000);
    
    core.generateVulnerabilitiesHTML(testData)
        .then(result => {
            completed = true;
            clearTimeout(timeout);
            const duration = Date.now() - startTime;
            
            console.log('Result length:', result.length);
            console.log('Result preview:', result.substring(0, 500));
            
            if (result && result.length > 2000) {
                // Check for actual generated content (not default text)
                const hasRealContent = !result.includes('ظ„ظ… ظٹطھظ… ط¥ظ†طھط§ط¬');
                const hasGeneratedDetails = result.includes('generateComprehensiveDetailsFromRealData');
                const hasGeneratedSteps = result.includes('generateRealExploitationStepsForVulnerabilityComprehensive');
                const hasGeneratedImpact = result.includes('generateDynamicImpactForAnyVulnerability');
                const hasImpactVisualizer = result.includes('impact_visualizer.js');
                const hasTextualAnalyzer = result.includes('textual_impact_analyzer.js');
                const hasActualData = result.includes('extracted_real_data');
                const hasComprehensiveAnalysis = result.includes('generateComprehensiveVulnerabilityAnalysis');
                const hasSecurityAnalysis = result.includes('generateDynamicSecurityImpactAnalysis');
                const hasRealTimeAssessment = result.includes('generateRealTimeVulnerabilityAssessment');
                const hasRiskAnalysis = result.includes('generateComprehensiveRiskAnalysis');
                const hasThreatModeling = result.includes('generateDynamicThreatModelingForVulnerability');
                
                const realContentScore = [
                    hasRealContent, hasGeneratedDetails, hasGeneratedSteps, hasGeneratedImpact,
                    hasImpactVisualizer, hasTextualAnalyzer, hasActualData, hasComprehensiveAnalysis,
                    hasSecurityAnalysis, hasRealTimeAssessment, hasRiskAnalysis, hasThreatModeling
                ].filter(Boolean).length;
                
                if (realContentScore >= 8) {
                    document.getElementById('result').innerHTML = 
                        'REAL_OUTPUT_SUCCESS_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        realContentScore + '_of_12_real_functions_working';
                } else {
                    document.getElementById('result').innerHTML = 
                        'REAL_OUTPUT_PARTIAL_' + 
                        result.length + '_chars_' + 
                        duration + 'ms_' + 
                        realContentScore + '_of_12_real_functions_working';
                }
            } else {
                document.getElementById('result').innerHTML = 'REAL_OUTPUT_FAILED_SHORT_RESULT_' + result.length;
            }
        })
        .catch(err => {
            completed = true;
            clearTimeout(timeout);
            if (err.message.includes('Maximum call stack size exceeded')) {
                document.getElementById('result').innerHTML = 'REAL_OUTPUT_FAILED_STACK_OVERFLOW';
            } else {
                document.getElementById('result').innerHTML = 'REAL_OUTPUT_ERROR_' + err.message.replace(/\s+/g, '_');
            }
        });
    
} catch(e) {
    document.getElementById('result').innerHTML = 'REAL_OUTPUT_SCRIPT_ERROR_' + e.message.replace(/\s+/g, '_');
}
</script>
</body></html>
